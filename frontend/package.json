{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "@monaco-editor/react": "^4.6.0", "react-split-pane": "^0.1.92", "diff2html": "^3.4.47", "prismjs": "^1.29.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}